"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/SettingsModal.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SettingsModal(param) {\n    let { isOpen, onClose, userData, onUserDataUpdate } = param;\n    _s();\n    const { logout, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"geral\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para cada aba\n    const [generalData, setGeneralData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: userData.username,\n        profileImage: userData.profileImage || \"\",\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    const [appearanceSettings, setAppearanceSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fonte: \"Inter\",\n        tamanhoFonte: 14,\n        palavrasPorSessao: 5000\n    });\n    const [aiEndpoints, setAiEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            nome: \"OpenRouter\",\n            url: \"https://openrouter.ai/api/v1/chat/completions\",\n            apiKey: \"\",\n            modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n            ativo: false\n        },\n        {\n            nome: \"DeepSeek\",\n            url: \"https://api.deepseek.com/v1/chat/completions\",\n            apiKey: \"\",\n            modeloPadrao: \"deepseek-chat\",\n            ativo: false\n        }\n    ]);\n    const [memories, setMemories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [memoryCategories, setMemoryCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddMemory, setShowAddMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddCategory, setShowAddCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMemory, setNewMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        titulo: \"\",\n        conteudo: \"\",\n        cor: \"#3B82F6\",\n        categoria: null,\n        chatId: null,\n        global: true\n    });\n    const [newCategory, setNewCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        descricao: \"\",\n        cor: \"#3B82F6\"\n    });\n    const [showAddEndpoint, setShowAddEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newEndpoint, setNewEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        url: \"\",\n        apiKey: \"\",\n        modeloPadrao: \"\",\n        ativo: false\n    });\n    const [editingEndpoint, setEditingEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editEndpointData, setEditEndpointData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        url: \"\",\n        apiKey: \"\",\n        modeloPadrao: \"\",\n        ativo: false\n    });\n    // Carregar configurações do Firestore\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadConfigurations = async ()=>{\n            if (!userData.username) return;\n            try {\n                console.log(\"Carregando configura\\xe7\\xf5es para:\", userData.username);\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\"));\n                if (configDoc.exists()) {\n                    const config = configDoc.data();\n                    console.log(\"Configura\\xe7\\xf5es carregadas:\", config);\n                    if (config.aparencia) {\n                        setAppearanceSettings(config.aparencia);\n                    }\n                    if (config.endpoints) {\n                        const endpointsArray = Object.values(config.endpoints);\n                        setAiEndpoints(endpointsArray);\n                    } else {\n                        // Manter endpoints padrão se não houver configuração salva\n                        setAiEndpoints([\n                            {\n                                nome: \"OpenRouter\",\n                                url: \"https://openrouter.ai/api/v1/chat/completions\",\n                                apiKey: \"\",\n                                modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n                                ativo: false\n                            },\n                            {\n                                nome: \"DeepSeek\",\n                                url: \"https://api.deepseek.com/v1/chat/completions\",\n                                apiKey: \"\",\n                                modeloPadrao: \"deepseek-chat\",\n                                ativo: false\n                            }\n                        ]);\n                    }\n                    if (config.memorias) {\n                        const memoriasArray = Object.values(config.memorias);\n                        setMemories(memoriasArray);\n                    }\n                    if (config.categorias) {\n                        const categoriasArray = Object.values(config.categorias);\n                        setMemoryCategories(categoriasArray);\n                    }\n                } else {\n                    console.log(\"Nenhuma configura\\xe7\\xe3o encontrada, usando padr\\xf5es\");\n                    // Configurações padrão se não existir documento\n                    setAppearanceSettings({\n                        fonte: \"Inter\",\n                        tamanhoFonte: 14,\n                        palavrasPorSessao: 5000\n                    });\n                }\n            } catch (error) {\n                console.error(\"Erro ao carregar configura\\xe7\\xf5es:\", error);\n            }\n        };\n        if (isOpen && userData.username) {\n            // Reset do estado do formulário geral quando abrir o modal\n            setGeneralData({\n                username: userData.username,\n                profileImage: userData.profileImage || \"\",\n                currentPassword: \"\",\n                newPassword: \"\",\n                confirmPassword: \"\"\n            });\n            loadConfigurations();\n        }\n    }, [\n        isOpen,\n        userData.username,\n        userData.profileImage\n    ]);\n    // Função auxiliar para deletar todos os dados do Storage de um usuário\n    const deleteUserStorageData = async (username)=>{\n        try {\n            console.log(\"Iniciando exclus\\xe3o de dados do Storage para:\", username);\n            // Deletar toda a pasta do usuário no Storage\n            const userStorageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.storage, \"usuarios/\".concat(username));\n            const userStorageList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.listAll)(userStorageRef);\n            // Função recursiva para deletar pastas e arquivos\n            const deleteRecursively = async (folderRef)=>{\n                const folderList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.listAll)(folderRef);\n                // Deletar todos os arquivos na pasta atual\n                const fileDeletePromises = folderList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.deleteObject)(item));\n                await Promise.all(fileDeletePromises);\n                // Deletar recursivamente todas as subpastas\n                const folderDeletePromises = folderList.prefixes.map((prefix)=>deleteRecursively(prefix));\n                await Promise.all(folderDeletePromises);\n            };\n            await deleteRecursively(userStorageRef);\n            console.log(\"Todos os dados do Storage deletados para:\", username);\n        } catch (error) {\n            console.log(\"Erro ao deletar dados do Storage ou pasta n\\xe3o encontrada:\", error);\n        }\n    };\n    // Função auxiliar para deletar recursivamente todos os documentos de um usuário\n    const deleteUserDocuments = async (username)=>{\n        try {\n            console.log(\"Iniciando exclus\\xe3o de documentos para:\", username);\n            // Deletar subcoleção de configurações\n            try {\n                const configDoc = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                const configSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(configDoc);\n                if (configSnapshot.exists()) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)(configDoc);\n                    console.log(\"Configura\\xe7\\xf5es deletadas\");\n                }\n            } catch (error) {\n                console.log(\"Erro ao deletar configura\\xe7\\xf5es:\", error);\n            }\n            // Deletar outras subcoleções se existirem (chats, histórico, etc.)\n            try {\n                const chatsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", username, \"chats\");\n                const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(chatsCollection);\n                const deletePromises = chatsSnapshot.docs.map((doc)=>(0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)(doc.ref));\n                await Promise.all(deletePromises);\n                console.log(\"Chats deletados\");\n            } catch (error) {\n                console.log(\"Erro ao deletar chats:\", error);\n            }\n            // Deletar documento principal do usuário\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", username);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)(userDocRef);\n            console.log(\"Documento principal do usu\\xe1rio deletado\");\n        } catch (error) {\n            console.error(\"Erro ao deletar documentos do usu\\xe1rio:\", error);\n            throw error;\n        }\n    };\n    // Atualizar username no documento principal\n    const updateUsername = async function(newUsername) {\n        let showSuccessAlert = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        if (!userData.username || !newUsername || newUsername === userData.username) {\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio inv\\xe1lido ou igual ao atual.\");\n            return false;\n        }\n        if (newUsername.length < 3) {\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio deve ter pelo menos 3 caracteres.\");\n            return false;\n        }\n        let newUserCreated = false;\n        try {\n            console.log(\"Atualizando username de\", userData.username, \"para\", newUsername);\n            // Verificar se o novo username já existe\n            const newUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername));\n            if (newUserDoc.exists()) {\n                if (showSuccessAlert) alert(\"Este nome de usu\\xe1rio j\\xe1 est\\xe1 em uso. Escolha outro.\");\n                return false;\n            }\n            // Buscar o documento atual pelo username antigo\n            const oldUserDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username);\n            const oldUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(oldUserDocRef);\n            if (!oldUserDoc.exists()) {\n                if (showSuccessAlert) alert(\"Usu\\xe1rio n\\xe3o encontrado.\");\n                return false;\n            }\n            const currentData = oldUserDoc.data();\n            // Criar novo documento com o novo username\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername), {\n                ...currentData,\n                username: newUsername,\n                updatedAt: new Date().toISOString()\n            });\n            newUserCreated = true;\n            console.log(\"Novo documento criado para:\", newUsername);\n            // Copiar todas as configurações e subcoleções\n            try {\n                // Copiar configurações principais\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\"));\n                if (configDoc.exists()) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername, \"configuracoes\", \"settings\"), configDoc.data());\n                    console.log(\"Configura\\xe7\\xf5es copiadas para novo username\");\n                }\n                // Copiar chats se existirem\n                try {\n                    const chatsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username, \"chats\");\n                    const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(chatsCollection);\n                    for (const chatDoc of chatsSnapshot.docs){\n                        const chatData = chatDoc.data();\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername, \"chats\", chatDoc.id), chatData);\n                    }\n                    if (chatsSnapshot.docs.length > 0) {\n                        console.log(\"\".concat(chatsSnapshot.docs.length, \" chats copiados para novo username\"));\n                    }\n                } catch (chatsError) {\n                    console.log(\"Erro ao copiar chats:\", chatsError);\n                }\n            } catch (configError) {\n                console.log(\"Erro ao copiar dados:\", configError);\n            }\n            // Deletar todos os documentos do usuário antigo\n            await deleteUserDocuments(userData.username);\n            console.log(\"Todos os documentos do usu\\xe1rio antigo foram deletados\");\n            // Atualizar estado local\n            onUserDataUpdate({\n                ...userData,\n                username: newUsername\n            });\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio atualizado com sucesso!\");\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar username:\", error);\n            // Se houve erro e o novo usuário foi criado, tentar fazer rollback\n            if (newUserCreated) {\n                try {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername));\n                    console.log(\"Rollback realizado - novo usu\\xe1rio deletado\");\n                } catch (rollbackError) {\n                    console.error(\"Erro no rollback:\", rollbackError);\n                }\n            }\n            if (showSuccessAlert) alert(\"Erro ao atualizar nome de usu\\xe1rio: \".concat(error instanceof Error ? error.message : \"Erro desconhecido\"));\n            return false;\n        }\n    };\n    // Salvar configurações no Firestore\n    const saveConfigurations = async ()=>{\n        if (!userData.username) {\n            alert(\"Erro: usu\\xe1rio n\\xe3o identificado\");\n            return;\n        }\n        try {\n            setLoading(true);\n            // Verificar se o username foi alterado e atualizá-lo primeiro\n            if (generalData.username !== userData.username) {\n                const usernameUpdated = await updateUsername(generalData.username, false);\n                if (!usernameUpdated) {\n                    // Se falhou ao atualizar o username, interromper o processo\n                    return;\n                }\n            }\n            // Determinar qual username usar (o novo se foi alterado)\n            const currentUsername = generalData.username !== userData.username ? generalData.username : userData.username;\n            const configData = {\n                aparencia: {\n                    fonte: appearanceSettings.fonte,\n                    tamanhoFonte: appearanceSettings.tamanhoFonte,\n                    palavrasPorSessao: appearanceSettings.palavrasPorSessao\n                },\n                endpoints: {},\n                memorias: {},\n                categorias: {},\n                updatedAt: new Date().toISOString()\n            };\n            // Converter arrays para objetos\n            aiEndpoints.forEach((endpoint, index)=>{\n                configData.endpoints[endpoint.nome || \"endpoint_\".concat(index)] = endpoint;\n            });\n            memories.forEach((memory, index)=>{\n                configData.memorias[\"memoria_\".concat(index)] = memory;\n            });\n            memoryCategories.forEach((category, index)=>{\n                configData.categorias[category.nome || \"categoria_\".concat(index)] = category;\n            });\n            console.log(\"Salvando configura\\xe7\\xf5es para:\", currentUsername);\n            console.log(\"Dados a serem salvos:\", configData);\n            // Usar setDoc com merge para não sobrescrever outros dados\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", currentUsername, \"configuracoes\", \"settings\");\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)(docRef, configData);\n            console.log(\"Configura\\xe7\\xf5es salvas com sucesso no Firestore\");\n            alert(\"Configura\\xe7\\xf5es salvas com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao salvar configura\\xe7\\xf5es:\", error);\n            alert(\"Erro ao salvar configura\\xe7\\xf5es: \".concat(error instanceof Error ? error.message : \"Erro desconhecido\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    // Funções utilitárias\n    const handleProfileImageUpload = async (file)=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const imageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.storage, \"usuarios/\".concat(userData.username, \"/profile.jpg\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.uploadBytes)(imageRef, file);\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.getDownloadURL)(imageRef);\n            setGeneralData((prev)=>({\n                    ...prev,\n                    profileImage: downloadURL\n                }));\n            // Atualizar no Firestore\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(userDocRef, {\n                profileImage: downloadURL\n            });\n            onUserDataUpdate({\n                ...userData,\n                profileImage: downloadURL\n            });\n            alert(\"Foto de perfil atualizada com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao fazer upload da imagem:\", error);\n            alert(\"Erro ao atualizar foto de perfil.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordChange = async ()=>{\n        if (!user || !generalData.currentPassword || !generalData.newPassword) {\n            alert(\"Preencha todos os campos de senha.\");\n            return;\n        }\n        if (generalData.newPassword !== generalData.confirmPassword) {\n            alert(\"As senhas n\\xe3o coincidem.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const credential = firebase_auth__WEBPACK_IMPORTED_MODULE_5__.EmailAuthProvider.credential(user.email, generalData.currentPassword);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_5__.reauthenticateWithCredential)(user, credential);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_5__.updatePassword)(user, generalData.newPassword);\n            setGeneralData((prev)=>({\n                    ...prev,\n                    currentPassword: \"\",\n                    newPassword: \"\",\n                    confirmPassword: \"\"\n                }));\n            alert(\"Senha alterada com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao alterar senha:\", error);\n            alert(\"Erro ao alterar senha. Verifique a senha atual.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        if (confirm(\"Tem certeza que deseja sair?\")) {\n            await logout();\n            onClose();\n        }\n    };\n    // Funções para gerenciar endpoints de IA\n    const handleAddEndpoint = ()=>{\n        if (!newEndpoint.nome || !newEndpoint.url || !newEndpoint.apiKey) {\n            alert(\"Preencha todos os campos obrigat\\xf3rios.\");\n            return;\n        }\n        setAiEndpoints((prev)=>[\n                ...prev,\n                {\n                    ...newEndpoint\n                }\n            ]);\n        setNewEndpoint({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n        setShowAddEndpoint(false);\n        alert(\"Endpoint adicionado com sucesso!\");\n    };\n    const handleToggleEndpoint = (index)=>{\n        setAiEndpoints((prev)=>prev.map((endpoint, i)=>i === index ? {\n                    ...endpoint,\n                    ativo: !endpoint.ativo\n                } : endpoint));\n    };\n    const handleDeleteEndpoint = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar este endpoint?\")) {\n            setAiEndpoints((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const handleEditEndpoint = (index)=>{\n        const endpoint = aiEndpoints[index];\n        setEditEndpointData({\n            ...endpoint\n        });\n        setEditingEndpoint(index);\n    };\n    const handleSaveEditEndpoint = ()=>{\n        if (editingEndpoint === null) return;\n        if (!editEndpointData.apiKey || !editEndpointData.modeloPadrao) {\n            alert(\"API Key e Modelo Padr\\xe3o s\\xe3o obrigat\\xf3rios.\");\n            return;\n        }\n        setAiEndpoints((prev)=>prev.map((endpoint, i)=>i === editingEndpoint ? {\n                    ...editEndpointData\n                } : endpoint));\n        setEditingEndpoint(null);\n        setEditEndpointData({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n        alert(\"Endpoint atualizado com sucesso!\");\n    };\n    const handleCancelEditEndpoint = ()=>{\n        setEditingEndpoint(null);\n        setEditEndpointData({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n    };\n    const handleTestEndpoint = async (endpoint)=>{\n        if (!endpoint.apiKey) {\n            alert(\"API Key \\xe9 necess\\xe1ria para testar o endpoint.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await fetch(endpoint.url, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(endpoint.apiKey)\n                },\n                body: JSON.stringify({\n                    model: endpoint.modeloPadrao || \"gpt-3.5-turbo\",\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: \"Test message\"\n                        }\n                    ],\n                    max_tokens: 10\n                })\n            });\n            if (response.ok) {\n                alert(\"✅ Endpoint testado com sucesso!\");\n            } else {\n                alert(\"❌ Erro ao testar endpoint. Verifique as configura\\xe7\\xf5es.\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao testar endpoint:\", error);\n            alert(\"❌ Erro ao conectar com o endpoint.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Funções para gerenciar memórias\n    const handleAddCategory = ()=>{\n        if (!newCategory.nome) {\n            alert(\"Nome da categoria \\xe9 obrigat\\xf3rio.\");\n            return;\n        }\n        setMemoryCategories((prev)=>[\n                ...prev,\n                {\n                    ...newCategory\n                }\n            ]);\n        setNewCategory({\n            nome: \"\",\n            descricao: \"\",\n            cor: \"#3B82F6\"\n        });\n        setShowAddCategory(false);\n        alert(\"Categoria criada com sucesso!\");\n    };\n    const handleAddMemory = ()=>{\n        if (!newMemory.titulo || !newMemory.conteudo) {\n            alert(\"T\\xedtulo e conte\\xfado s\\xe3o obrigat\\xf3rios.\");\n            return;\n        }\n        setMemories((prev)=>[\n                ...prev,\n                {\n                    ...newMemory\n                }\n            ]);\n        setNewMemory({\n            titulo: \"\",\n            conteudo: \"\",\n            cor: \"#3B82F6\",\n            categoria: null,\n            chatId: null,\n            global: true\n        });\n        setShowAddMemory(false);\n        alert(\"Mem\\xf3ria criada com sucesso!\");\n    };\n    const handleDeleteMemory = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar esta mem\\xf3ria?\")) {\n            setMemories((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const handleDeleteCategory = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar esta categoria?\")) {\n            setMemoryCategories((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const colors = [\n        \"#3B82F6\",\n        \"#EF4444\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#8B5CF6\",\n        \"#EC4899\",\n        \"#06B6D4\",\n        \"#84CC16\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                exit: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                className: \"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-white/20 rounded-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden mx-4 lg:mx-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 lg:p-6 border-b border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl lg:text-2xl font-bold text-white\",\n                                        children: \"Configura\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm lg:hidden mt-1\",\n                                        children: [\n                                            activeTab === \"geral\" && \"Informa\\xe7\\xf5es pessoais e senha\",\n                                            activeTab === \"aparencia\" && \"Personaliza\\xe7\\xe3o da interface\",\n                                            activeTab === \"ia\" && \"Endpoints de intelig\\xeancia artificial\",\n                                            activeTab === \"memoria\" && \"Sistema de mem\\xf3rias\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 705,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-64 bg-white/5 border-b lg:border-b-0 lg:border-r border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"p-2 lg:p-4 space-y-1 lg:space-y-2 overflow-x-auto lg:overflow-x-visible\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2 min-w-max lg:min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"geral\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"geral\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Geral\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"aparencia\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"aparencia\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Apar\\xeancia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"ia\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"ia\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"IA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"memoria\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"memoria\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Mem\\xf3ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 lg:p-6 overflow-y-auto max-h-[calc(95vh-200px)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: [\n                                        activeTab === \"geral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Configura\\xe7\\xf5es Gerais\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Foto de Perfil\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: generalData.profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: generalData.profileImage,\n                                                                            alt: \"Profile\",\n                                                                            className: \"w-20 h-20 rounded-full object-cover border-2 border-white/20\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 812,\n                                                                            columnNumber: 33\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white font-bold text-2xl\",\n                                                                                children: userData.username.charAt(0).toUpperCase()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 819,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 818,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    var _fileInputRef_current;\n                                                                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                                                },\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                                children: \"Alterar Foto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 826,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/60 text-sm mt-2\",\n                                                                                children: \"JPG, PNG ou GIF. M\\xe1ximo 5MB.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 833,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: fileInputRef,\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) handleProfileImageUpload(file);\n                                                                },\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nome de Usu\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: generalData.username,\n                                                                        onChange: (e)=>setGeneralData((prev)=>({\n                                                                                    ...prev,\n                                                                                    username: e.target.value\n                                                                                })),\n                                                                        className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"Digite seu nome de usu\\xe1rio\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 854,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    generalData.username !== userData.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-yellow-300 text-sm\",\n                                                                            children: '⚠️ Nome de usu\\xe1rio alterado. Clique em \"Salvar Configura\\xe7\\xf5es\" para aplicar as mudan\\xe7as.'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 865,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 864,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 853,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Alterar Senha\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Senha Atual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 878,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.currentPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            currentPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Digite sua senha atual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 881,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 877,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nova Senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 892,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.newPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            newPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Digite sua nova senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 895,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 891,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Confirmar Nova Senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 906,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.confirmPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            confirmPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Confirme sua nova senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 909,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 905,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handlePasswordChange,\n                                                                        disabled: loading,\n                                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: loading ? \"Alterando...\" : \"Alterar Senha\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 919,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 803,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"geral\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"aparencia\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Configura\\xe7\\xf5es de Apar\\xeancia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 942,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Fonte do Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Fam\\xedlia da Fonte\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 949,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: appearanceSettings.fonte,\n                                                                                onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                            ...prev,\n                                                                                            fonte: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Inter\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Inter\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 959,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Roboto\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Roboto\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 960,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"JetBrains Mono\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"JetBrains Mono\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 961,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Lato\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Lato\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 962,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Fira Code\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Fira Code\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 963,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Merriweather\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Merriweather\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 964,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Open Sans\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Open Sans\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 965,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Source Sans Pro\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Source Sans Pro\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 966,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Poppins\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Poppins\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 967,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Nunito\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Nunito\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 968,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 952,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 948,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-lg p-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-sm mb-2\",\n                                                                                children: \"Pr\\xe9-visualiza\\xe7\\xe3o:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 973,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-white p-3 bg-white/5 rounded border border-white/10\",\n                                                                                style: {\n                                                                                    fontFamily: appearanceSettings.fonte,\n                                                                                    fontSize: \"\".concat(appearanceSettings.tamanhoFonte, \"px\")\n                                                                                },\n                                                                                children: \"Esta \\xe9 uma mensagem de exemplo para visualizar a fonte selecionada. Lorem ipsum dolor sit amet, consectetur adipiscing elit.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 974,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 972,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 947,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Tamanho da Fonte\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                            children: [\n                                                                                \"Tamanho: \",\n                                                                                appearanceSettings.tamanhoFonte,\n                                                                                \"px\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 990,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"range\",\n                                                                            min: \"10\",\n                                                                            max: \"24\",\n                                                                            value: appearanceSettings.tamanhoFonte,\n                                                                            onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                        ...prev,\n                                                                                        tamanhoFonte: parseInt(e.target.value)\n                                                                                    })),\n                                                                            className: \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 993,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"10px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1002,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"24px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1003,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1001,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                    lineNumber: 989,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 986,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Sess\\xf5es de Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1011,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"text-white font-medium\",\n                                                                                        children: \"Divis\\xe3o Autom\\xe1tica\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1015,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white/60 text-sm\",\n                                                                                        children: \"Dividir chats longos em sess\\xf5es baseadas na contagem de palavras\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1016,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1014,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"bg-blue-600 relative inline-flex h-6 w-11 items-center rounded-full transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1023,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1020,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1013,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: [\n                                                                                    \"Palavras por Sess\\xe3o: \",\n                                                                                    appearanceSettings.palavrasPorSessao.toLocaleString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1028,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"range\",\n                                                                                min: \"1000\",\n                                                                                max: \"20000\",\n                                                                                step: \"500\",\n                                                                                value: appearanceSettings.palavrasPorSessao,\n                                                                                onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                            ...prev,\n                                                                                            palavrasPorSessao: parseInt(e.target.value)\n                                                                                        })),\n                                                                                className: \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1031,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"1.000\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1041,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"20.000\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1042,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1040,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1027,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-blue-300 text-sm\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCA1 \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Dica:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1048,\n                                                                                    columnNumber: 36\n                                                                                }, this),\n                                                                                \" Sess\\xf5es menores carregam mais r\\xe1pido, mas podem fragmentar conversas longas. Recomendamos entre 3.000-8.000 palavras para melhor experi\\xeancia.\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1047,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1046,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1012,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"aparencia\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"ia\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Intelig\\xeancia Artificial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80\",\n                                                                children: \"Gerencie seus endpoints de IA personalizados\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1071,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddEndpoint(!showAddEndpoint),\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 4v16m8-8H4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1080,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1079,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Adicionar Endpoint\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1082,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1074,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1070,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    showAddEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Novo Endpoint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1094,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nome do Endpoint *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1097,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newEndpoint.nome,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            nome: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Meu Endpoint\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1100,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1096,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"URL do Endpoint *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1111,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"url\",\n                                                                                value: newEndpoint.url,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            url: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"https://api.exemplo.com/v1/chat/completions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1114,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1110,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"API Key *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1125,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: newEndpoint.apiKey,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            apiKey: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"sk-...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1128,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1124,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Modelo Padr\\xe3o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1139,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newEndpoint.modeloPadrao,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            modeloPadrao: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"gpt-3.5-turbo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1142,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1138,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddEndpoint(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1154,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddEndpoint,\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Adicionar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1160,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: aiEndpoints.map((endpoint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-3 h-3 rounded-full \".concat(endpoint.ativo ? \"bg-green-500\" : \"bg-gray-500\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1177,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-lg font-semibold text-white\",\n                                                                                        children: endpoint.nome\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1178,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    (endpoint.nome === \"OpenRouter\" || endpoint.nome === \"DeepSeek\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full\",\n                                                                                        children: \"Pr\\xe9-configurado\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1180,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1176,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleToggleEndpoint(index),\n                                                                                        className: \"px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 \".concat(endpoint.ativo ? \"bg-green-600 hover:bg-green-700 text-white\" : \"bg-gray-600 hover:bg-gray-700 text-white\"),\n                                                                                        children: endpoint.ativo ? \"Ativo\" : \"Inativo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1186,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleTestEndpoint(endpoint),\n                                                                                        disabled: loading || !endpoint.apiKey,\n                                                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Testar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1196,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleEditEndpoint(index),\n                                                                                        className: \"bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Editar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1204,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    endpoint.nome !== \"OpenRouter\" && endpoint.nome !== \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteEndpoint(index),\n                                                                                        className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Deletar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1212,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1185,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1175,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"URL:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1225,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white font-mono text-xs break-all\",\n                                                                                        children: endpoint.url\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1226,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1224,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"Modelo:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1229,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white\",\n                                                                                        children: endpoint.modeloPadrao || \"N\\xe3o especificado\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1230,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1228,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"API Key:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1233,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white font-mono text-xs\",\n                                                                                        children: endpoint.apiKey ? \"••••••••••••\" + endpoint.apiKey.slice(-4) : \"N\\xe3o configurada\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1234,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1232,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"Status:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1239,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium \".concat(endpoint.ativo ? \"text-green-400\" : \"text-gray-400\"),\n                                                                                        children: endpoint.ativo ? \"Ativo\" : \"Inativo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1240,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1238,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1223,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    editingEndpoint === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            height: 0\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            height: \"auto\"\n                                                                        },\n                                                                        exit: {\n                                                                            opacity: 0,\n                                                                            height: 0\n                                                                        },\n                                                                        className: \"mt-4 pt-4 border-t border-white/10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"text-white font-semibold mb-4\",\n                                                                                children: \"Editar Endpoint\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1254,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                                children: \"API Key *\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1257,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"password\",\n                                                                                                value: editEndpointData.apiKey,\n                                                                                                onChange: (e)=>setEditEndpointData((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            apiKey: e.target.value\n                                                                                                        })),\n                                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                                placeholder: \"Cole sua API Key aqui...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1260,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1256,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                                children: \"Modelo Padr\\xe3o *\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1271,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: editEndpointData.modeloPadrao,\n                                                                                                onChange: (e)=>setEditEndpointData((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            modeloPadrao: e.target.value\n                                                                                                        })),\n                                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                                placeholder: \"Ex: gpt-4, claude-3-sonnet, etc.\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1274,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1270,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1255,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-end space-x-2 mt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleCancelEditEndpoint,\n                                                                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Cancelar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1286,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleSaveEditEndpoint,\n                                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Salvar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1293,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1285,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1248,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    (endpoint.nome === \"OpenRouter\" || endpoint.nome === \"DeepSeek\") && !endpoint.apiKey && editingEndpoint !== index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 pt-4 border-t border-white/10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Configure sua API Key:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1307,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"password\",\n                                                                                        placeholder: \"Cole sua API Key aqui...\",\n                                                                                        className: \"flex-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                        onChange: (e)=>{\n                                                                                            const newKey = e.target.value;\n                                                                                            setAiEndpoints((prev)=>prev.map((ep, i)=>i === index ? {\n                                                                                                        ...ep,\n                                                                                                        apiKey: newKey\n                                                                                                    } : ep));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1311,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleToggleEndpoint(index),\n                                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Salvar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1324,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1310,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1306,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1174,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1172,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"ia\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 1059,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"memoria\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Sistema de Mem\\xf3ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddCategory(!showAddCategory),\n                                                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1360,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Nova Categoria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1363,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1354,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddMemory(!showAddMemory),\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1371,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1370,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Nova Mem\\xf3ria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1374,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1353,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    showAddCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nova Categoria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1386,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nome da Categoria *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1389,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newCategory.nome,\n                                                                                onChange: (e)=>setNewCategory((prev)=>({\n                                                                                            ...prev,\n                                                                                            nome: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Trabalho, Pessoal, Projetos...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1392,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1388,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Descri\\xe7\\xe3o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1403,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                value: newCategory.descricao,\n                                                                                onChange: (e)=>setNewCategory((prev)=>({\n                                                                                            ...prev,\n                                                                                            descricao: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                                                rows: 3,\n                                                                                placeholder: \"Descreva o prop\\xf3sito desta categoria...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1406,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1402,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Cor da Categoria\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1417,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: colors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>setNewCategory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    cor: color\n                                                                                                })),\n                                                                                        className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(newCategory.cor === color ? \"border-white scale-110\" : \"border-white/30\"),\n                                                                                        style: {\n                                                                                            backgroundColor: color\n                                                                                        }\n                                                                                    }, color, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1422,\n                                                                                        columnNumber: 37\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1420,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1416,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1387,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddCategory(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1435,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddCategory,\n                                                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Criar Categoria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1441,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1434,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1380,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    showAddMemory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nova Mem\\xf3ria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1460,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"T\\xedtulo da Mem\\xf3ria *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1463,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newMemory.titulo,\n                                                                                onChange: (e)=>setNewMemory((prev)=>({\n                                                                                            ...prev,\n                                                                                            titulo: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Informa\\xe7\\xf5es importantes sobre...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1466,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1462,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Conte\\xfado *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1477,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                value: newMemory.conteudo,\n                                                                                onChange: (e)=>setNewMemory((prev)=>({\n                                                                                            ...prev,\n                                                                                            conteudo: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                                                rows: 4,\n                                                                                placeholder: \"Digite o conte\\xfado da mem\\xf3ria...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1480,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1476,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                        children: \"Categoria\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1492,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: newMemory.categoria || \"\",\n                                                                                        onChange: (e)=>setNewMemory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    categoria: e.target.value || null\n                                                                                                })),\n                                                                                        className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"\",\n                                                                                                className: \"bg-gray-800\",\n                                                                                                children: \"Sem categoria\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1502,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            memoryCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: category.nome,\n                                                                                                    className: \"bg-gray-800\",\n                                                                                                    children: category.nome\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                    lineNumber: 1504,\n                                                                                                    columnNumber: 39\n                                                                                                }, this))\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1495,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1491,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                        children: \"Cor da Mem\\xf3ria\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1511,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex space-x-2\",\n                                                                                        children: colors.slice(0, 4).map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>setNewMemory((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            cor: color\n                                                                                                        })),\n                                                                                                className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(newMemory.cor === color ? \"border-white scale-110\" : \"border-white/30\"),\n                                                                                                style: {\n                                                                                                    backgroundColor: color\n                                                                                                }\n                                                                                            }, color, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1516,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1514,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1510,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1490,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: newMemory.global,\n                                                                                        onChange: (e)=>setNewMemory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    global: e.target.checked\n                                                                                                })),\n                                                                                        className: \"w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500 focus:ring-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1530,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/80 text-sm\",\n                                                                                        children: \"Mem\\xf3ria Global\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1537,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1529,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/50 text-xs\",\n                                                                                children: \"Mem\\xf3rias globais ficam dispon\\xedveis em todos os chats\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1539,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1528,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1461,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddMemory(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1545,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddMemory,\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Criar Mem\\xf3ria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1551,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1544,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1454,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    memoryCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Categorias\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1565,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                                children: memoryCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-4 h-4 rounded-full\",\n                                                                                                style: {\n                                                                                                    backgroundColor: category.cor\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1571,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"text-white font-medium\",\n                                                                                                children: category.nome\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1575,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1570,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteCategory(index),\n                                                                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1582,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                            lineNumber: 1581,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1577,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1569,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            category.descricao && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/60 text-sm\",\n                                                                                children: category.descricao\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1588,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1568,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1566,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1564,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: [\n                                                                    \"Mem\\xf3rias (\",\n                                                                    memories.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1598,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            memories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white/5 border border-white/10 rounded-xl p-8 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-12 h-12 text-white/40 mx-auto mb-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1604,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1603,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60\",\n                                                                        children: \"Nenhuma mem\\xf3ria criada ainda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1607,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/40 text-sm mt-1\",\n                                                                        children: 'Clique em \"Nova Mem\\xf3ria\" para come\\xe7ar'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1608,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1602,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: memories.map((memory, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-start justify-between mb-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-3\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-4 h-4 rounded-full flex-shrink-0\",\n                                                                                                style: {\n                                                                                                    backgroundColor: memory.cor\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1618,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                        className: \"text-white font-semibold\",\n                                                                                                        children: memory.titulo\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                        lineNumber: 1623,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex items-center space-x-2 mt-1\",\n                                                                                                        children: [\n                                                                                                            memory.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full\",\n                                                                                                                children: memory.categoria\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                                lineNumber: 1626,\n                                                                                                                columnNumber: 45\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(memory.global ? \"bg-green-500/20 text-green-300\" : \"bg-orange-500/20 text-orange-300\"),\n                                                                                                                children: memory.global ? \"Global\" : \"Chat Espec\\xedfico\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                                lineNumber: 1630,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                        lineNumber: 1624,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1622,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1617,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteMemory(index),\n                                                                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1645,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                            lineNumber: 1644,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1640,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1616,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-sm leading-relaxed\",\n                                                                                children: memory.conteudo\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1650,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1615,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1613,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1597,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"memoria\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 1342,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-t border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                    children: \"Sair da Conta\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 1668,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 1667,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"px-6 py-2 text-white/70 hover:text-white transition-colors font-medium\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 1680,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveConfigurations,\n                                        disabled: loading,\n                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                        children: loading ? \"Salvando...\" : \"Salvar Configura\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 1686,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 1679,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 1666,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                lineNumber: 694,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n            lineNumber: 688,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n        lineNumber: 686,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsModal, \"NlknMWS5D7wJlDA9TWR9YqAOHHE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SettingsModal;\nvar _c;\n$RefreshReg$(_c, \"SettingsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx\n"));

/***/ })

});