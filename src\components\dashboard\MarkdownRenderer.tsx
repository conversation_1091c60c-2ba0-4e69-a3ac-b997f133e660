'use client';

import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeHighlight from 'rehype-highlight';

// Importar estilos do KaTeX e Markdown
import 'katex/dist/katex.min.css';
import '@/styles/markdown-latex.css';
import '@/styles/markdown-advanced.css';

interface WebSearchAnnotation {
  type: "url_citation";
  url: string;
  title: string;
  content?: string;
  start_index: number;
  end_index: number;
}

interface MarkdownRendererProps {
  content: string;
  className?: string;
  hasWebSearch?: boolean;
  webSearchAnnotations?: WebSearchAnnotation[];
  isStreaming?: boolean; // Nova prop para indicar se está em streaming
}

// Componente otimizado para renderização de markdown durante streaming
const OptimizedMarkdown = React.memo(({ content, isStreaming }: { content: string; isStreaming: boolean }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[
        rehypeKatex,
        [rehypeHighlight, { detect: true, ignoreMissing: true }]
      ]}
      components={{
        // Customizar renderização de código
        code({ node, inline, className, children, ...props }: any) {
          const match = /language-(\w+)/.exec(className || '');
          return !inline && match ? (
            <pre className="bg-gray-800 rounded-lg p-4 overflow-x-auto">
              <code className={className} {...props}>
                {children}
              </code>
            </pre>
          ) : (
            <code
              className="bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono"
              {...props}
            >
              {children}
            </code>
          );
        },

        // Customizar renderização de links
        a({ children, href, ...props }) {
          return (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 underline"
              {...props}
            >
              {children}
            </a>
          );
        },

        // Customizar renderização de tabelas
        table({ children, ...props }) {
          return (
            <div className="overflow-x-auto my-4">
              <table className="min-w-full border-collapse border border-gray-600" {...props}>
                {children}
              </table>
            </div>
          );
        },

        th({ children, ...props }) {
          return (
            <th className="border border-gray-600 px-4 py-2 bg-gray-700 text-left font-semibold" {...props}>
              {children}
            </th>
          );
        },

        td({ children, ...props }) {
          return (
            <td className="border border-gray-600 px-4 py-2" {...props}>
              {children}
            </td>
          );
        },

        // Customizar renderização de blockquotes
        blockquote({ children, ...props }) {
          return (
            <blockquote className="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-900/20 rounded-r-lg" {...props}>
              {children}
            </blockquote>
          );
        },

        // Customizar renderização de listas
        ul({ children, ...props }) {
          return (
            <ul className="list-disc list-inside space-y-1 my-2" {...props}>
              {children}
            </ul>
          );
        },

        ol({ children, ...props }) {
          return (
            <ol className="list-decimal list-inside space-y-1 my-2" {...props}>
              {children}
            </ol>
          );
        },

        // Customizar renderização de títulos
        h1({ children, ...props }) {
          return (
            <h1 className="text-2xl font-bold mb-4 mt-6 text-white border-b border-gray-600 pb-2" {...props}>
              {children}
            </h1>
          );
        },

        h2({ children, ...props }) {
          return (
            <h2 className="text-xl font-bold mb-3 mt-5 text-white" {...props}>
              {children}
            </h2>
          );
        },

        h3({ children, ...props }) {
          return (
            <h3 className="text-lg font-bold mb-2 mt-4 text-white" {...props}>
              {children}
            </h3>
          );
        },

        // Customizar renderização de parágrafos
        p({ children, ...props }) {
          return (
            <p className="mb-3 leading-relaxed text-gray-100" {...props}>
              {children}
            </p>
          );
        }
      }}
    >
      {content}
    </ReactMarkdown>
  );
}, (prevProps, nextProps) => {
  // Durante streaming, só re-renderizar se o conteúdo mudou significativamente
  if (nextProps.isStreaming) {
    // Durante streaming, re-renderizar apenas a cada 100 caracteres para melhor performance
    const prevLength = prevProps.content.length;
    const nextLength = nextProps.content.length;
    const shouldUpdate = nextLength - prevLength >= 100 || nextLength < prevLength;
    return !shouldUpdate;
  }
  // Fora do streaming, comportamento normal
  return prevProps.content === nextProps.content;
});

const MarkdownRenderer: React.FC<MarkdownRendererProps> = React.memo(({
  content,
  className = '',
  hasWebSearch = false,
  webSearchAnnotations = [],
  isStreaming = false
}) => {
  // Função para detectar se o conteúdo contém citações de web search
  const detectWebSearch = (text: string): boolean => {
    // Detecta links no formato [dominio.com] que são característicos do web search
    const webSearchPattern = /\[[\w.-]+\.[\w]+\]/g;
    const matches = text.match(webSearchPattern);
    return matches !== null && matches.length > 0;
  };

  // Função para processar o conteúdo (OpenRouter já retorna links formatados corretamente)
  const processWebSearchLinks = (text: string): string => {
    // Como o OpenRouter já retorna os links no formato markdown correto,
    // não precisamos processar nada. Apenas retornamos o texto original.
    return text;
  };

  // Função para contar e extrair informações sobre as fontes
  const getWebSearchInfo = (text: string, annotations: WebSearchAnnotation[]): { sourceCount: number; sources: string[] } => {
    if (annotations.length > 0) {
      // Usar annotations se disponíveis
      const uniqueDomains = new Set(annotations.map(annotation => {
        try {
          return new URL(annotation.url).hostname.replace('www.', '');
        } catch (e) {
          return annotation.url;
        }
      }));

      return {
        sourceCount: annotations.length,
        sources: Array.from(uniqueDomains)
      };
    }

    // Fallback: detectar pelos padrões no texto (formato markdown link)
    const webSearchPattern = /\[[\w.-]+\.[\w]+\]\([^)]+\)/g;
    const matches = text.match(webSearchPattern) || [];
    const sourceSet = new Set(matches.map(match => {
      // Extrair o domínio do formato [dominio.com](url)
      const domainMatch = match.match(/\[([\w.-]+\.[\w]+)\]/);
      return domainMatch ? domainMatch[1] : match;
    }));
    const uniqueSources = Array.from(sourceSet);

    return {
      sourceCount: matches.length,
      sources: uniqueSources
    };
  };

  // Usar useMemo para otimizar processamento durante streaming
  const { isWebSearchMessage, webSearchInfo, processedContent } = useMemo(() => {
    const isWebSearch = hasWebSearch || detectWebSearch(content);
    const searchInfo = isWebSearch ? getWebSearchInfo(content, webSearchAnnotations) : { sourceCount: 0, sources: [] };
    const processed = isWebSearch ? processWebSearchLinks(content) : content;

    return {
      isWebSearchMessage: isWebSearch,
      webSearchInfo: searchInfo,
      processedContent: processed
    };
  }, [content, hasWebSearch, webSearchAnnotations]);

  // Classe CSS condicional para desabilitar animações durante streaming
  const streamingClass = isStreaming ? 'streaming-mode' : '';

  return (
    <div className={`markdown-content ${className} ${streamingClass}`}>
      {/* Seção de Web Search estilizada */}
      {isWebSearchMessage && (
        <div className="web-search-section mb-4 p-3 rounded-lg bg-gradient-to-r from-cyan-900/20 to-blue-900/20 border border-cyan-500/30 backdrop-blur-sm">
          <div className="flex items-center gap-2 mb-2">
            <svg className="w-4 h-4 text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z" />
            </svg>
            <span className="text-cyan-300 text-sm font-medium">🌐 Busca na Web Ativada</span>
          </div>
          <div className="text-xs text-cyan-200/80 leading-relaxed">
            <span className="text-cyan-300 font-medium">
              {webSearchInfo.sourceCount} citação{webSearchInfo.sourceCount !== 1 ? 'ões' : ''} de {webSearchInfo.sources.length} fonte{webSearchInfo.sources.length !== 1 ? 's' : ''}
            </span>
            {webSearchInfo.sources.length > 0 && (
              <div className="mt-1 flex flex-wrap gap-1">
                {webSearchInfo.sources.map((source, index) => (
                  <span key={index} className="inline-block px-2 py-0.5 bg-cyan-600/20 text-cyan-200 rounded text-xs border border-cyan-500/30">
                    {source}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Conteúdo principal */}
      <OptimizedMarkdown
        content={processedContent}
        isStreaming={isStreaming}
      />
    </div>
  );
});

export default MarkdownRenderer;
