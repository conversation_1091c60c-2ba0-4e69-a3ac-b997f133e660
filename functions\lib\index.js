"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatWithAI = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const node_fetch_1 = __importDefault(require("node-fetch"));
// Inicializar Firebase Admin
admin.initializeApp();
/**
 * Função para buscar configurações do usuário
 * @param {string} username - Nome do usuário
 * @return {Promise<UserSettings | null>} Configurações do usuário
 */
async function getUserSettings(username) {
    try {
        const settingsDoc = await admin.firestore()
            .collection("usuarios")
            .doc(username)
            .collection("configuracoes")
            .doc("settings")
            .get();
        if (!settingsDoc.exists) {
            console.error(`Configurações não encontradas para usuário: ${username}`);
            return null;
        }
        return settingsDoc.data();
    }
    catch (error) {
        console.error("Erro ao buscar configurações do usuário:", error);
        return null;
    }
}
/**
 * Função para buscar configurações do chat
 * @param {string} username - Nome do usuário
 * @param {string} chatId - ID do chat
 * @return {Promise<ChatConfig | null>} Configurações do chat
 */
async function getChatConfig(username, chatId) {
    try {
        const chatDoc = await admin.firestore()
            .collection("usuarios")
            .doc(username)
            .collection("conversas")
            .doc(chatId)
            .get();
        if (!chatDoc.exists) {
            console.error(`Chat não encontrado: ${chatId}`);
            return null;
        }
        const data = chatDoc.data();
        return {
            systemPrompt: (data === null || data === void 0 ? void 0 : data.systemPrompt) || "",
            context: (data === null || data === void 0 ? void 0 : data.context) || "",
            temperature: (data === null || data === void 0 ? void 0 : data.temperature) || 1.0,
            frequencyPenalty: (data === null || data === void 0 ? void 0 : data.frequencyPenalty) || 1.0,
            repetitionPenalty: (data === null || data === void 0 ? void 0 : data.repetitionPenalty) || 1.0,
            maxTokens: (data === null || data === void 0 ? void 0 : data.maxTokens) || 2048,
            lastUsedModel: (data === null || data === void 0 ? void 0 : data.lastUsedModel) || "",
            latexInstructions: (data === null || data === void 0 ? void 0 : data.latexInstructions) || false,
        };
    }
    catch (error) {
        console.error("Erro ao buscar configurações do chat:", error);
        return null;
    }
}
/**
 * Função para carregar chat.json do Storage
 * @param {string} username - Nome do usuário
 * @param {string} chatId - ID do chat
 * @return {Promise<ChatData | null>} Dados do chat
 */
async function loadChatData(username, chatId) {
    try {
        const bucket = admin.storage().bucket();
        const filePath = `usuarios/${username}/conversas/${chatId}/chat.json`;
        const file = bucket.file(filePath);
        const [exists] = await file.exists();
        if (!exists) {
            console.error(`Arquivo chat.json não encontrado: ${filePath}`);
            return null;
        }
        const [contents] = await file.download();
        const chatData = JSON.parse(contents.toString());
        return chatData;
    }
    catch (error) {
        console.error("Erro ao carregar chat.json:", error);
        return null;
    }
}
/**
 * Função para salvar chat.json atualizado
 * @param {string} username - Nome do usuário
 * @param {string} chatId - ID do chat
 * @param {ChatData} chatData - Dados do chat
 * @return {Promise<boolean>} Sucesso da operação
 */
async function saveChatData(username, chatId, chatData) {
    try {
        const bucket = admin.storage().bucket();
        const filePath = `usuarios/${username}/conversas/${chatId}/chat.json`;
        const file = bucket.file(filePath);
        // Atualizar timestamp
        chatData.lastUpdated = new Date().toISOString();
        // Salvar arquivo
        await file.save(JSON.stringify(chatData, null, 2), {
            metadata: {
                contentType: "application/json",
            },
        });
        return true;
    }
    catch (error) {
        console.error("Erro ao salvar chat.json:", error);
        return false;
    }
}
/**
 * Prompt LaTeX para instruir a IA
 */
const LATEX_INSTRUCTIONS_PROMPT = `# INSTRUÇÕES PARA USO DE LaTeX

Use LaTeX para expressões matemáticas:

## REGRAS BÁSICAS:
1. Use \`$...$\` para fórmulas inline: $x^2 + y^2 = z^2$
2. Use \`$$...$$\` para fórmulas em bloco:
$$\\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

## SINTAXE COMUM:
- Frações: \`$\\frac{a}{b}$\`
- Potências: \`$x^2$\`, \`$x^{n+1}$\`
- Subscritos: \`$a_1$\`, \`$x_{i+1}$\`
- Raízes: \`$\\sqrt{x}$\`, \`$\\sqrt[3]{x}$\`
- Símbolos gregos: \`$\\pi$\`, \`$\\alpha$\`, \`$\\beta$\`

## EXEMPLOS:
- Correto: "A área do círculo é \`$A = \\pi r^2$\`"
- Correto: "Equação quadrática: \`$ax^2 + bx + c = 0$\`"

Use LaTeX sempre que houver matemática, mas mantenha o texto normal legível.`;
/**
 * Prepara anexos para envio ao OpenRouter
 */
function prepareAttachmentsForOpenRouter(attachments) {
    var _a;
    logInfo("=== DEBUG: PREPARANDO ANEXOS PARA OPENROUTER ===");
    logInfo("Número de anexos:", attachments.length);
    // Filtrar apenas anexos ativos
    const activeAttachments = attachments.filter(att => att.isActive !== false);
    logInfo("Anexos ativos após filtragem:", activeAttachments.length);
    const openRouterContent = [];
    for (let i = 0; i < activeAttachments.length; i++) {
        const attachment = activeAttachments[i];
        logInfo(`Processando anexo ativo ${i + 1}:`, {
            id: attachment.id,
            type: attachment.type,
            filename: attachment.filename,
            hasUrl: !!attachment.url,
            hasBase64: !!attachment.base64Data,
            isActive: attachment.isActive,
            urlPreview: ((_a = attachment.url) === null || _a === void 0 ? void 0 : _a.substring(0, 50)) + "..."
        });
        if (attachment.type === 'image') {
            // Para imagens, usar URL
            const imageContent = {
                type: 'image_url',
                image_url: {
                    url: attachment.url
                }
            };
            openRouterContent.push(imageContent);
            logInfo(`Anexo ${i + 1} adicionado como imagem:`, imageContent);
        }
        else if (attachment.type === 'pdf' && attachment.base64Data) {
            // Para PDFs, usar base64 com formato file
            const dataUrl = `data:application/pdf;base64,${attachment.base64Data}`;
            const pdfContent = {
                type: 'file',
                file: {
                    filename: attachment.filename,
                    file_data: dataUrl
                }
            };
            openRouterContent.push(pdfContent);
            logInfo(`Anexo ${i + 1} adicionado como PDF:`, {
                type: pdfContent.type,
                filename: pdfContent.file.filename,
                dataUrlLength: dataUrl.length
            });
        }
        else {
            logWarning(`Anexo ${i + 1} ignorado - tipo não suportado ou dados faltando:`, {
                type: attachment.type,
                hasBase64: !!attachment.base64Data,
                hasUrl: !!attachment.url
            });
        }
    }
    logInfo("Total de anexos processados para OpenRouter:", openRouterContent.length);
    return openRouterContent;
}
/**
 * Processa memórias do usuário e retorna o conteúdo para adicionar ao System Prompt
 * @param {Record<string, Memory>} memorias - Memórias do usuário
 * @param {string} chatId - ID do chat atual
 * @return {string} Conteúdo das memórias formatado para o prompt
 */
function processUserMemories(memorias, chatId) {
    if (!memorias || Object.keys(memorias).length === 0) {
        return "";
    }
    const relevantMemories = [];
    // Filtrar memórias relevantes (globais ou específicas do chat)
    Object.values(memorias).forEach(memory => {
        if (memory.global || memory.chatId === chatId) {
            relevantMemories.push(memory);
        }
    });
    if (relevantMemories.length === 0) {
        return "";
    }
    // Construir o conteúdo das memórias
    let memoriesContent = "=== MEMÓRIAS DO USUÁRIO ===\n";
    memoriesContent += "As seguintes informações são memórias importantes do usuário que devem ser consideradas:\n\n";
    relevantMemories.forEach((memory, index) => {
        memoriesContent += `${index + 1}. **${memory.titulo}**\n`;
        memoriesContent += `   ${memory.conteudo}\n`;
        if (memory.categoria) {
            memoriesContent += `   [Categoria: ${memory.categoria}]\n`;
        }
        memoriesContent += `   [Escopo: ${memory.global ? 'Global' : 'Chat Específico'}]\n\n`;
    });
    memoriesContent += "Use essas memórias para personalizar suas respostas e manter consistência com as preferências e informações do usuário.\n";
    memoriesContent += "=== FIM DAS MEMÓRIAS ===\n";
    return memoriesContent;
}
/**
 * Prepara plugins para PDFs (engine mistral-ocr)
 * Verifica PDFs tanto na mensagem atual quanto no histórico (apenas anexos ativos)
 */
function preparePDFPlugins(currentAttachments, chatMessages) {
    // Verificar PDFs na mensagem atual (apenas ativos)
    let hasPDF = currentAttachments.some(att => att.type === 'pdf' && att.isActive !== false);
    // Se não tem PDF na mensagem atual, verificar no histórico (apenas anexos ativos)
    if (!hasPDF) {
        hasPDF = chatMessages.some(msg => msg.attachments && msg.attachments.some(att => att.type === 'pdf' && att.isActive !== false));
    }
    if (!hasPDF) {
        return [];
    }
    console.log("=== DEBUG: PDF ENCONTRADO - ADICIONANDO PLUGIN MISTRAL-OCR ===");
    return [
        {
            id: 'file-parser',
            pdf: {
                engine: 'mistral-ocr'
            }
        }
    ];
}
/**
 * Sistema de Chain of Thought (CoT)
 */
function buildCoTPrompt(messages, systemPrompt, context, latexInstructions = false, currentAttachments = [], userMemories = "") {
    const cotMessages = [];
    // Adicionar instruções LaTeX se habilitado
    if (latexInstructions) {
        cotMessages.push({
            role: "system",
            content: LATEX_INSTRUCTIONS_PROMPT,
        });
    }
    // Adicionar memórias do usuário se existirem
    if (userMemories.trim()) {
        cotMessages.push({
            role: "system",
            content: userMemories,
        });
    }
    // Adicionar system prompt se existir
    if (systemPrompt.trim()) {
        cotMessages.push({
            role: "system",
            content: systemPrompt,
        });
    }
    // Adicionar contexto se existir
    if (context.trim()) {
        cotMessages.push({
            role: "system",
            content: `Contexto adicional: ${context}`,
        });
    }
    // Processar mensagens existentes para CoT
    if (messages.length > 0) {
        // Adicionar instrução de CoT para conversas existentes
        const cotInstruction = "Continue esta conversa de forma fluida e " +
            "concisa. Mantenha o contexto das mensagens anteriores e " +
            "responda de forma natural e útil.";
        cotMessages.push({
            role: "system",
            content: cotInstruction,
        });
        // Adicionar histórico de mensagens (limitado para evitar excesso)
        const recentMessages = messages.slice(-10); // Últimas 10 mensagens
        for (const message of recentMessages) {
            // Preparar conteúdo da mensagem
            const messageContent = [];
            // Adicionar texto se não estiver vazio
            if (message.content && message.content.trim().length > 0) {
                messageContent.push({
                    type: "text",
                    text: message.content,
                });
            }
            // NOTA: Anexos históricos não são incluídos automaticamente
            // Apenas anexos da mensagem atual são processados via parâmetro 'attachments'
            // Isso evita duplicação e permite controle granular via frontend
            // Determinar o conteúdo final
            let finalContent;
            if (messageContent.length === 0) {
                // Se não há conteúdo nem anexos, usar texto vazio
                finalContent = "";
            }
            else if (messageContent.length === 1 && messageContent[0].type === "text") {
                // Se há apenas texto, usar string simples
                finalContent = message.content;
            }
            else {
                // Se há anexos ou múltiplos elementos, usar array
                finalContent = messageContent;
            }
            cotMessages.push({
                role: message.role,
                content: finalContent,
            });
        }
    }
    else {
        // Para conversas novas, adicionar instrução inicial
        const initialInstruction = "Esta é uma nova conversa. Responda de " +
            "forma útil, clara e concisa. Seja amigável e profissional.";
        cotMessages.push({
            role: "system",
            content: initialInstruction,
        });
    }
    // Adicionar anexos da mensagem atual (se houver)
    if (currentAttachments.length > 0) {
        logInfo("=== DEBUG: ADICIONANDO ANEXOS DA MENSAGEM ATUAL ===");
        logInfo("Número de anexos atuais:", currentAttachments.length);
        const attachmentContent = prepareAttachmentsForOpenRouter(currentAttachments);
        if (attachmentContent.length > 0) {
            // Adicionar anexos à última mensagem (que será a mensagem atual do usuário)
            const lastMessage = cotMessages[cotMessages.length - 1];
            if (lastMessage && lastMessage.role === "user") {
                // Se a última mensagem é do usuário, adicionar anexos a ela
                if (typeof lastMessage.content === "string") {
                    // Converter string para array com texto + anexos
                    lastMessage.content = [
                        { type: "text", text: lastMessage.content },
                        ...attachmentContent
                    ];
                }
                else if (Array.isArray(lastMessage.content)) {
                    // Adicionar anexos ao array existente
                    lastMessage.content.push(...attachmentContent);
                }
                logInfo("Anexos adicionados à mensagem do usuário");
            }
            else {
                // Se não há mensagem do usuário, criar uma nova apenas com anexos
                cotMessages.push({
                    role: "user",
                    content: attachmentContent
                });
                logInfo("Nova mensagem criada apenas com anexos");
            }
        }
    }
    return cotMessages;
}
/**
 * Função para atualizar Firestore após nova mensagem
 * @param {string} username - Nome do usuário
 * @param {string} chatId - ID do chat
 * @param {string} lastMessage - Última mensagem
 * @return {Promise<boolean>} Sucesso da operação
 */
async function updateChatInFirestore(username, chatId, lastMessage) {
    try {
        const now = new Date().toISOString();
        await admin.firestore()
            .collection("usuarios")
            .doc(username)
            .collection("conversas")
            .doc(chatId)
            .update({
            ultimaMensagem: lastMessage,
            ultimaMensagemEm: now,
            lastUpdatedAt: now,
            updatedAt: now,
        });
        return true;
    }
    catch (error) {
        console.error("Erro ao atualizar chat no Firestore:", error);
        return false;
    }
}
/**
 * Função para gerar ID único para mensagens
 * @return {string} ID único
 */
function generateMessageId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}
/**
 * Função para validar entrada da requisição
 * @param {unknown} body - Corpo da requisição
 * @return {Object} Resultado da validação
 */
function validateRequest(body) {
    if (!body) {
        return { isValid: false, error: "Corpo da requisição é obrigatório" };
    }
    const { username, chatId, message, attachments } = body;
    if (!username || typeof username !== "string" ||
        username.trim().length === 0) {
        return {
            isValid: false,
            error: "Username é obrigatório e deve ser uma string não vazia",
        };
    }
    if (!chatId || typeof chatId !== "string" || chatId.trim().length === 0) {
        return {
            isValid: false,
            error: "ChatId é obrigatório e deve ser uma string não vazia",
        };
    }
    // Mensagem pode estar vazia se houver anexos
    const hasAttachments = Array.isArray(attachments) && attachments.length > 0;
    const hasMessage = message && typeof message === "string" && message.trim().length > 0;
    if (!hasMessage && !hasAttachments) {
        return {
            isValid: false,
            error: "É necessário fornecer uma mensagem ou anexos",
        };
    }
    // Se há mensagem, validar o tipo
    if (message && typeof message !== "string") {
        return {
            isValid: false,
            error: "Message deve ser uma string",
        };
    }
    // Validar tamanho da mensagem se ela existir
    if (message && typeof message === "string" && message.length > 10000) {
        return {
            isValid: false,
            error: "Mensagem muito longa (máximo 10.000 caracteres)",
        };
    }
    return { isValid: true };
}
/**
 * Função para log estruturado
 * @param {string} message - Mensagem de log
 * @param {unknown} data - Dados adicionais
 */
function logInfo(message, data) {
    functions.logger.info(message, data);
    console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : "");
}
/**
 * Função para log de erro
 * @param {string} message - Mensagem de erro
 * @param {unknown} error - Erro
 */
function logError(message, error) {
    console.error(`[ERROR] ${message}`, error);
}
/**
 * Função para log de warning
 * @param {string} message - Mensagem de warning
 * @param {unknown} data - Dados adicionais
 */
function logWarning(message, data) {
    console.warn(`[WARNING] ${message}`, data ? JSON.stringify(data, null, 2) : "");
}
// Função principal para streaming com OpenRouter
exports.chatWithAI = functions.https.onRequest(async (req, res) => {
    var _a, e_1, _b, _c;
    var _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w;
    // Configurar CORS
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    if (req.method === "OPTIONS") {
        res.status(200).send("");
        return;
    }
    if (req.method !== "POST") {
        res.status(405).json({ error: "Método não permitido" });
        return;
    }
    try {
        logInfo("=== DEBUG: NOVA REQUISIÇÃO RECEBIDA ===");
        logInfo("Method: " + req.method);
        logInfo("Headers", req.headers);
        logInfo("Body completo", req.body);
        logInfo("Body type: " + typeof req.body);
        // Validar entrada
        const validation = validateRequest(req.body);
        if (!validation.isValid) {
            console.log("=== DEBUG: VALIDAÇÃO FALHOU ===");
            console.log("Erro de validação:", validation.error);
            logWarning("Requisição inválida", {
                error: validation.error,
                body: req.body,
            });
            res.status(400).json({ error: validation.error });
            return;
        }
        const { username, chatId, message, model, attachments = [], isRegeneration = false, webSearchEnabled = false } = req.body;
        logInfo("=== DEBUG: DADOS EXTRAÍDOS DA REQUISIÇÃO ===");
        logInfo("Username: " + username + " (tipo: " + typeof username + ")");
        logInfo("ChatId: " + chatId + " (tipo: " + typeof chatId + ")");
        logInfo("Message length: " + (message === null || message === void 0 ? void 0 : message.length) + " (tipo: " + typeof message + ")");
        logInfo("Model: " + model + " (tipo: " + typeof model + ")");
        logInfo("Attachments count: " + attachments.length + " (tipo: " + typeof attachments + ")");
        logInfo("IsRegeneration: " + isRegeneration + " (tipo: " + typeof isRegeneration + ")");
        logInfo("WebSearchEnabled: " + webSearchEnabled + " (tipo: " + typeof webSearchEnabled + ")");
        logInfo("Message preview: " + ((message === null || message === void 0 ? void 0 : message.substring(0, 100)) + ((message === null || message === void 0 ? void 0 : message.length) > 100 ? "..." : "")));
        logInfo("Processando requisição", {
            username,
            chatId,
            messageLength: message.length,
            model,
        });
        // Buscar configurações do usuário
        const userSettings = await getUserSettings(username);
        if (!userSettings) {
            logError("Configurações do usuário não encontradas", { username });
            res.status(404).json({ error: "Configurações do usuário não encontradas" });
            return;
        }
        // Buscar configurações do chat
        const chatConfig = await getChatConfig(username, chatId);
        if (!chatConfig) {
            logError("Chat não encontrado", { username, chatId });
            res.status(404).json({ error: "Chat não encontrado" });
            return;
        }
        // Carregar dados do chat
        const chatData = await loadChatData(username, chatId);
        if (!chatData) {
            logError("Dados do chat não encontrados", { username, chatId });
            res.status(404).json({ error: "Dados do chat não encontrados" });
            return;
        }
        // Encontrar endpoint ativo
        const activeEndpoint = Object.values(userSettings.endpoints)
            .find((endpoint) => endpoint.ativo);
        if (!activeEndpoint || !activeEndpoint.apiKey) {
            logError("Endpoint ativo não encontrado ou sem API key", {
                username,
                hasEndpoints: Object.keys(userSettings.endpoints).length > 0,
                activeEndpoints: Object.values(userSettings.endpoints)
                    .filter((e) => e.ativo).length,
            });
            const errorMsg = "Nenhum endpoint ativo encontrado ou " +
                "API key não configurada";
            res.status(400).json({ error: errorMsg });
            return;
        }
        // Usar modelo especificado ou padrão do endpoint
        const selectedModel = model || chatConfig.lastUsedModel ||
            activeEndpoint.modeloPadrao;
        // Adicionar mensagem do usuário ao chat apenas se não for regeneração
        if (!isRegeneration) {
            const userMessage = {
                id: generateMessageId(),
                content: message || "", // Garantir que content nunca seja undefined
                role: "user",
                timestamp: new Date().toISOString(),
                attachments: attachments.length > 0 ? attachments : undefined,
            };
            chatData.messages.push(userMessage);
        }
        // Log da mensagem do usuário
        if (!isRegeneration) {
            const lastMessage = chatData.messages[chatData.messages.length - 1];
            logInfo("=== DEBUG: MENSAGEM DO USUÁRIO ADICIONADA ===");
            logInfo("Mensagem do usuário:", {
                id: lastMessage.id,
                role: lastMessage.role,
                contentLength: ((_d = lastMessage.content) === null || _d === void 0 ? void 0 : _d.length) || 0,
                hasAttachments: !!(lastMessage.attachments && lastMessage.attachments.length > 0),
                attachmentsCount: ((_e = lastMessage.attachments) === null || _e === void 0 ? void 0 : _e.length) || 0,
                attachmentsDetails: ((_f = lastMessage.attachments) === null || _f === void 0 ? void 0 : _f.map(att => ({
                    id: att.id,
                    type: att.type,
                    filename: att.filename,
                    hasBase64: !!att.base64Data
                }))) || []
            });
        }
        else {
            logInfo("=== DEBUG: REGENERAÇÃO - MENSAGEM DO USUÁRIO NÃO ADICIONADA ===");
            logInfo("Usando mensagem existente para regeneração");
        }
        // Log das mensagens carregadas do histórico
        logInfo("=== DEBUG: MENSAGENS DO HISTÓRICO ===");
        logInfo("Total de mensagens no chat:", chatData.messages.length);
        chatData.messages.forEach((msg, index) => {
            var _a, _b;
            logInfo(`Mensagem ${index + 1}:`, {
                id: msg.id,
                role: msg.role,
                contentLength: ((_a = msg.content) === null || _a === void 0 ? void 0 : _a.length) || 0,
                hasAttachments: !!(msg.attachments && msg.attachments.length > 0),
                attachmentsCount: ((_b = msg.attachments) === null || _b === void 0 ? void 0 : _b.length) || 0
            });
            if (msg.attachments && msg.attachments.length > 0) {
                msg.attachments.forEach((att, attIndex) => {
                    logInfo(`  Anexo ${attIndex + 1}:`, {
                        id: att.id,
                        type: att.type,
                        filename: att.filename,
                        hasBase64: !!att.base64Data
                    });
                });
            }
        });
        // Processar memórias do usuário
        const userMemoriesContent = processUserMemories(userSettings.memorias, chatId);
        // Log das memórias processadas
        if (userMemoriesContent.trim()) {
            logInfo("=== DEBUG: MEMÓRIAS DO USUÁRIO PROCESSADAS ===");
            logInfo("Número de memórias encontradas:", Object.keys(userSettings.memorias).length);
            logInfo("Conteúdo das memórias (primeiros 200 chars):", userMemoriesContent.substring(0, 200) + "...");
        }
        // Construir prompt com CoT
        const cotMessages = buildCoTPrompt(chatData.messages, chatConfig.systemPrompt, chatConfig.context, chatConfig.latexInstructions, attachments, // Passar anexos da mensagem atual
        userMemoriesContent // Passar memórias processadas
        );
        // Os anexos já foram processados corretamente na função buildCoTPrompt
        // Vamos adicionar logs para debug
        if (attachments.length > 0) {
            logInfo("=== DEBUG: ANEXOS DETECTADOS ===");
            logInfo("Número de anexos:", attachments.length);
            for (let i = 0; i < attachments.length; i++) {
                const att = attachments[i];
                logInfo(`Anexo ${i + 1}:`, {
                    id: att.id,
                    type: att.type,
                    filename: att.filename,
                    url: ((_g = att.url) === null || _g === void 0 ? void 0 : _g.substring(0, 50)) + "...",
                    hasBase64: !!att.base64Data
                });
            }
            // Verificar se a última mensagem do CoT tem anexos
            const lastMessage = cotMessages[cotMessages.length - 1];
            logInfo("Última mensagem do CoT:", {
                role: lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.role,
                contentType: typeof (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.content),
                isArray: Array.isArray(lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.content),
                contentPreview: Array.isArray(lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.content)
                    ? `Array com ${lastMessage.content.length} elementos`
                    : (_h = lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.content) === null || _h === void 0 ? void 0 : _h.substring(0, 100)
            });
        }
        // Preparar payload para OpenRouter
        const payload = {
            model: selectedModel,
            messages: cotMessages,
            stream: true,
            temperature: chatConfig.temperature,
            frequency_penalty: chatConfig.frequencyPenalty,
            presence_penalty: chatConfig.repetitionPenalty,
            max_tokens: chatConfig.maxTokens,
            usage: {
                include: true
            }
        };
        // Adicionar plugins para PDFs se necessário (verifica mensagem atual + histórico)
        const pdfPlugins = preparePDFPlugins(attachments, chatData.messages);
        if (pdfPlugins.length > 0) {
            payload.plugins = pdfPlugins;
            logInfo("=== DEBUG: PLUGINS PDF ADICIONADOS ===", pdfPlugins);
        }
        // Adicionar web search se habilitado
        if (webSearchEnabled) {
            logInfo("=== DEBUG: WEB SEARCH HABILITADO ===");
            // Se já tem plugins (PDF), adicionar web search aos plugins existentes
            if (payload.plugins) {
                payload.plugins.push({
                    id: "web",
                    max_results: 5
                });
            }
            else {
                // Se não tem plugins, criar array com web search
                payload.plugins = [{
                        id: "web",
                        max_results: 5
                    }];
            }
            // Adicionar web_search_options para modelos que suportam (sempre usar High como solicitado)
            payload.web_search_options = {
                search_context_size: "high"
            };
            logInfo("=== DEBUG: WEB SEARCH PLUGIN ADICIONADO ===", {
                plugins: payload.plugins,
                web_search_options: payload.web_search_options
            });
        }
        // Log do payload final (sem mostrar todo o conteúdo para não poluir)
        logInfo("=== DEBUG: PAYLOAD FINAL ===", {
            model: payload.model,
            messagesCount: payload.messages.length,
            hasPlugins: !!payload.plugins,
            pluginsCount: ((_j = payload.plugins) === null || _j === void 0 ? void 0 : _j.length) || 0,
            hasWebSearch: webSearchEnabled,
            webSearchOptions: payload.web_search_options,
            lastMessagePreview: payload.messages[payload.messages.length - 1]
        });
        logInfo("=== DEBUG: INICIANDO REQUISIÇÃO PARA OPENROUTER ===");
        logInfo("URL:", activeEndpoint.url);
        logInfo("Headers preparados");
        // DEBUG: Log detalhado de todos os parâmetros
        logInfo("=== DEBUG: PARÂMETROS COMPLETOS DA REQUISIÇÃO ===");
        logInfo("1. ENDPOINT ATIVO", {
            nome: activeEndpoint.nome,
            url: activeEndpoint.url,
            apiKeyPreview: ((_k = activeEndpoint.apiKey) === null || _k === void 0 ? void 0 : _k.substring(0, 10)) + "...",
            modeloPadrao: activeEndpoint.modeloPadrao,
            ativo: activeEndpoint.ativo
        });
        logInfo("2. MODELO SELECIONADO", {
            modeloRequest: model,
            ultimoModeloChat: chatConfig.lastUsedModel,
            modeloPadraoEndpoint: activeEndpoint.modeloPadrao,
            MODELO_FINAL_SELECIONADO: selectedModel
        });
        logInfo("3. CONFIGURAÇÕES DO CHAT", {
            temperature: chatConfig.temperature,
            temperatureType: typeof chatConfig.temperature,
            frequencyPenalty: chatConfig.frequencyPenalty,
            frequencyPenaltyType: typeof chatConfig.frequencyPenalty,
            repetitionPenalty: chatConfig.repetitionPenalty,
            repetitionPenaltyType: typeof chatConfig.repetitionPenalty,
            maxTokens: chatConfig.maxTokens,
            maxTokensType: typeof chatConfig.maxTokens,
            systemPromptLength: ((_l = chatConfig.systemPrompt) === null || _l === void 0 ? void 0 : _l.length) || 0,
            contextLength: ((_m = chatConfig.context) === null || _m === void 0 ? void 0 : _m.length) || 0,
            latexInstructions: chatConfig.latexInstructions
        });
        console.log("4. MENSAGENS CoT:");
        console.log("   - Total de mensagens:", cotMessages.length);
        cotMessages.forEach((msg, index) => {
            const msgObj = msg;
            console.log(`   - Mensagem ${index + 1}:`, {
                role: msgObj.role,
                contentType: typeof msgObj.content,
                contentLength: typeof msgObj.content === 'string' ? msgObj.content.length : Array.isArray(msgObj.content) ? msgObj.content.length : 0,
                contentPreview: typeof msgObj.content === 'string'
                    ? msgObj.content.substring(0, 100) + (msgObj.content.length > 100 ? "..." : "")
                    : Array.isArray(msgObj.content)
                        ? `[Array com ${msgObj.content.length} elementos]`
                        : '[Conteúdo não-string]'
            });
        });
        console.log("5. PAYLOAD FINAL:");
        console.log("   - Model:", payload.model);
        console.log("   - Stream:", payload.stream);
        console.log("   - Temperature:", payload.temperature, "(tipo:", typeof payload.temperature, ")");
        console.log("   - Frequency Penalty:", payload.frequency_penalty, "(tipo:", typeof payload.frequency_penalty, ")");
        console.log("   - Presence Penalty:", payload.presence_penalty, "(tipo:", typeof payload.presence_penalty, ")");
        console.log("   - Max Tokens:", payload.max_tokens, "(tipo:", typeof payload.max_tokens, ")");
        console.log("   - Messages count:", payload.messages.length);
        console.log("6. HEADERS DA REQUISIÇÃO:");
        const headers = {
            "Authorization": `Bearer ${activeEndpoint.apiKey}`,
            "Content-Type": "application/json",
        };
        console.log("   - Authorization: Bearer", ((_o = activeEndpoint.apiKey) === null || _o === void 0 ? void 0 : _o.substring(0, 10)) + "...");
        console.log("   - Content-Type:", headers["Content-Type"]);
        console.log("7. PAYLOAD JSON (primeiros 500 chars):");
        const payloadJson = JSON.stringify(payload);
        console.log("   - JSON length:", payloadJson.length);
        console.log("   - JSON preview:", payloadJson.substring(0, 500) + (payloadJson.length > 500 ? "..." : ""));
        console.log("=== FIM DEBUG PARÂMETROS ===");
        logInfo("Fazendo requisição para API", {
            url: activeEndpoint.url,
            model: selectedModel,
            messagesCount: cotMessages.length,
            temperature: chatConfig.temperature,
            maxTokens: chatConfig.maxTokens,
        });
        // Fazer requisição para OpenRouter
        console.log("=== DEBUG: FAZENDO REQUISIÇÃO ===");
        console.log("URL:", activeEndpoint.url);
        console.log("Method: POST");
        console.log("Enviando requisição...");
        let response;
        try {
            logInfo("=== DEBUG: TENTANDO FETCH ===");
            console.log("Payload usage config:", JSON.stringify(payload.usage, null, 2));
            response = await (0, node_fetch_1.default)(activeEndpoint.url, {
                method: "POST",
                headers: {
                    "Authorization": `Bearer ${activeEndpoint.apiKey}`,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(payload),
            });
            logInfo("=== DEBUG: FETCH CONCLUÍDO COM SUCESSO ===");
        }
        catch (fetchError) {
            logError("=== DEBUG: ERRO NO FETCH ===", fetchError);
            throw fetchError;
        }
        console.log("=== DEBUG: RESPOSTA RECEBIDA ===");
        console.log("Status:", response.status);
        console.log("Status Text:", response.statusText);
        console.log("Headers:", Object.fromEntries(response.headers.entries()));
        console.log("OK:", response.ok);
        if (!response.ok) {
            const errorText = await response.text();
            console.log("=== DEBUG: ERRO NA API ===");
            console.log("Error Text:", errorText);
            console.log("Tentando fazer parse do erro como JSON...");
            try {
                const errorJson = JSON.parse(errorText);
                console.log("Erro JSON parseado:", JSON.stringify(errorJson, null, 2));
            }
            catch (e) {
                console.log("Erro não é JSON válido, texto puro:", errorText);
            }
            logError("Erro na API externa", {
                status: response.status,
                statusText: response.statusText,
                error: errorText,
                url: activeEndpoint.url,
                model: selectedModel,
            });
            res.status(response.status).json({ error: `Erro na API: ${errorText}` });
            return;
        }
        // Configurar streaming
        res.setHeader("Content-Type", "text/plain; charset=utf-8");
        res.setHeader("Cache-Control", "no-cache");
        res.setHeader("Connection", "keep-alive");
        let assistantMessage = "";
        let buffer = "";
        let chunkCount = 0;
        let totalDataEvents = 0;
        let contentChunks = 0;
        let webSearchAnnotations = [];
        const requestStartTime = Date.now();
        // Processar stream
        if (response.body) {
            // Usar ReadableStream do Node.js
            const stream = response.body;
            try {
                try {
                    for (var _x = true, stream_1 = __asyncValues(stream), stream_1_1; stream_1_1 = await stream_1.next(), _a = stream_1_1.done, !_a; _x = true) {
                        _c = stream_1_1.value;
                        _x = false;
                        const chunk = _c;
                        chunkCount++;
                        const chunkStr = chunk.toString();
                        buffer += chunkStr;
                        if (chunkCount <= 5) {
                            console.log(`Chunk ${chunkCount} recebido (${chunkStr.length} chars):`, chunkStr.substring(0, 200) + (chunkStr.length > 200 ? "..." : ""));
                        }
                        // Processar linhas completas do buffer
                        let lineEnd = buffer.indexOf("\n");
                        while (lineEnd !== -1) {
                            const line = buffer.slice(0, lineEnd).trim();
                            buffer = buffer.slice(lineEnd + 1);
                            if (line.startsWith("data: ")) {
                                totalDataEvents++;
                                const data = line.slice(6);
                                if (totalDataEvents <= 10) {
                                    console.log(`Data event ${totalDataEvents}:`, data.substring(0, 200) + (data.length > 200 ? "..." : ""));
                                }
                                if (data === "[DONE]") {
                                    console.log("Stream finalizado com [DONE]");
                                    break;
                                }
                                // Ignorar comentários SSE
                                if (data.startsWith(":")) {
                                    console.log("Comentário SSE ignorado:", data);
                                    lineEnd = buffer.indexOf("\n");
                                    continue;
                                }
                                try {
                                    const parsed = JSON.parse(data);
                                    if (totalDataEvents <= 5) {
                                        console.log(`Parsed JSON ${totalDataEvents}:`, JSON.stringify(parsed, null, 2));
                                    }
                                    const content = (_r = (_q = (_p = parsed.choices) === null || _p === void 0 ? void 0 : _p[0]) === null || _q === void 0 ? void 0 : _q.delta) === null || _r === void 0 ? void 0 : _r.content;
                                    if (content) {
                                        contentChunks++;
                                        assistantMessage += content;
                                        res.write(content);
                                        if (contentChunks <= 5) {
                                            console.log(`Content chunk ${contentChunks}:`, JSON.stringify(content));
                                        }
                                    }
                                    else if ((_t = (_s = parsed.choices) === null || _s === void 0 ? void 0 : _s[0]) === null || _t === void 0 ? void 0 : _t.delta) {
                                        // Log outros tipos de delta que não são content
                                        const delta = parsed.choices[0].delta;
                                        if (totalDataEvents <= 10) {
                                            console.log(`Delta sem content ${totalDataEvents}:`, JSON.stringify(delta));
                                        }
                                    }
                                    // Capturar annotations de web search (se disponíveis)
                                    if ((_w = (_v = (_u = parsed.choices) === null || _u === void 0 ? void 0 : _u[0]) === null || _v === void 0 ? void 0 : _v.message) === null || _w === void 0 ? void 0 : _w.annotations) {
                                        const annotations = parsed.choices[0].message.annotations;
                                        for (const annotation of annotations) {
                                            if (annotation.type === "url_citation" && annotation.url_citation) {
                                                const webAnnotation = {
                                                    type: "url_citation",
                                                    url: annotation.url_citation.url,
                                                    title: annotation.url_citation.title || annotation.url_citation.url,
                                                    content: annotation.url_citation.content,
                                                    start_index: annotation.url_citation.start_index || 0,
                                                    end_index: annotation.url_citation.end_index || 0
                                                };
                                                webSearchAnnotations.push(webAnnotation);
                                            }
                                        }
                                    }
                                    // Log se há erros na resposta
                                    if (parsed.error) {
                                        console.log("ERRO na resposta da API:", JSON.stringify(parsed.error, null, 2));
                                    }
                                }
                                catch (e) {
                                    // Ignorar JSON inválido
                                    console.log("JSON inválido no stream:", {
                                        data: data.substring(0, 200),
                                        error: e instanceof Error ? e.message : String(e)
                                    });
                                    logWarning("JSON inválido ignorado no stream", { data, error: e });
                                }
                            }
                            else if (line.trim() && !line.startsWith(":")) {
                                console.log("Linha não-data ignorada:", line);
                            }
                            lineEnd = buffer.indexOf("\n");
                        }
                    }
                }
                catch (e_1_1) { e_1 = { error: e_1_1 }; }
                finally {
                    try {
                        if (!_x && !_a && (_b = stream_1.return)) await _b.call(stream_1);
                    }
                    finally { if (e_1) throw e_1.error; }
                }
            }
            catch (streamError) {
                console.log("=== DEBUG: ERRO NO STREAM ===");
                console.log("Stream error:", streamError);
                console.log("Buffer atual:", buffer.substring(0, 500));
                console.log("Mensagem parcial:", assistantMessage.substring(0, 200));
                logError("Erro no processamento do stream", streamError);
                if (!res.headersSent) {
                    res.status(500).json({ error: "Erro no processamento do stream" });
                    return;
                }
            }
        }
        else {
            console.log("=== DEBUG: SEM BODY NA RESPOSTA ===");
            console.log("Response body é null/undefined");
        }
        // Salvar resposta da IA no chat
        if (assistantMessage.trim()) {
            // Calcular tempo de resposta
            const responseTime = Date.now() - requestStartTime;
            const aiMessage = {
                id: generateMessageId(),
                content: assistantMessage.trim(),
                role: "assistant",
                timestamp: Date.now(),
                webSearchAnnotations: webSearchAnnotations.length > 0 ? webSearchAnnotations : undefined,
                responseTime: responseTime
            };
            chatData.messages.push(aiMessage);
            // Salvar chat atualizado
            const chatSaved = await saveChatData(username, chatId, chatData);
            if (!chatSaved) {
                logError("Falha ao salvar chat.json", { username, chatId });
            }
            // Atualizar Firestore
            const firestoreUpdated = await updateChatInFirestore(username, chatId, assistantMessage.trim());
            if (!firestoreUpdated) {
                logError("Falha ao atualizar Firestore", { username, chatId });
            }
            logInfo("Conversa processada com sucesso", {
                username,
                chatId,
                messageLength: assistantMessage.length,
                chatSaved,
                firestoreUpdated,
            });
        }
        else {
            logWarning("Resposta da IA vazia", { username, chatId });
        }
        // Finalizar stream HTTP
        res.end();
    }
    catch (error) {
        logError("Erro no processamento geral", error);
        if (!res.headersSent) {
            res.status(500).json({ error: "Erro interno do servidor" });
        }
    }
});
//# sourceMappingURL=index.js.map